import express from 'express'
import { gameG1Service } from '@/services'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { AppDataSource } from '@/config/config'
import { LogActivity } from '@/entities/LogActivity'

// Helper function để lấy thời gian GMT+7
const getNowGMT7 = () => {
  return new Date(new Date().getTime() + 7 * 60 * 60 * 1000)
}

// Helper function để log activity
const logActivityRepo = () => AppDataSource.getRepository(LogActivity)

async function logActivity(
  userId: number, 
  name: string, 
  url: string, 
  method: string, 
  requestData: any, 
  result: any, 
  ip?: string, 
  agent?: string
) {
  try {
    const log = new LogActivity()
    log.user_id = userId
    log.name = name
    log.message = `GameG1 API ${name} - Status: ${result?.status || 'unknown'}`
    log.full_message = JSON.stringify(result)
    log.url = url
    log.method = method
    log.ip = ip
    log.agent = agent
    log.form_data = JSON.stringify(requestData)
    log.created_at = getNowGMT7()
    log.updated_at = getNowGMT7()
    
    await logActivityRepo().save(log)
  } catch (error) {
    console.error('Error logging activity:', error)
  }
}

/**
 * 1. GET /api/game/init
 * Initialize game, preload user data and game UI
 */
const initGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  const startTime = Date.now()
  let result: any = null
  
  try {
    const { slug } = req.params
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug, params: req.params, query: req.query }

    if (!slug) {
      result = { 
        status: 'error', 
        message: 'Missing game slug' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'initGame',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    result = await gameG1Service.initGame(req, slug)
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'initGame',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug: req.params.slug, params: req.params, query: req.query }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'initGame - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

/**
 * 2. GET /api/game/play
 * Start the game and get vouchers that will fall
 */
const playGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  let result: any = null
  
  try {
    const { slug } = req.params
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug, params: req.params, query: req.query }

    if (!slug) {
      result = { 
        status: 'error', 
        message: 'Missing game slug' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'playGame',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    result = await gameG1Service.playGame(req, slug)
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'playGame',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug: req.params.slug, params: req.params, query: req.query }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'playGame - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

/**
 * 3. POST /api/game/claim
 * Claim a voucher by tapping it
 */
const claimVoucherHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  let result: any = null

  try {
    const { slug } = req.params
    const { box_ids: boxIds, context } = req.body
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug, boxIds, context, params: req.params, body: req.body }

    if (!slug) {
      result = {
        status: 'error',
        message: 'Missing game slug'
      }

      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'claimVoucher',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }

      return res.status(200).json(result)
    }

    result = await gameG1Service.claimVoucher(
      req,
      slug,
      boxIds,
      context
    )
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'claimVoucher',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    console.log('claimVoucherHandler error', error)
    const userId = req.authorizedUser?.user?.id
    const requestData = {
      slug: req.params.slug,
      boxIds: req.body.box_ids,
      context: req.body.context,
      params: req.params,
      body: req.body
    }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'claimVoucher - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

/**
 * 4. POST /api/game/share
 * Share to receive 1 extra play turn (1 time per day)
 */
const shareForExtraTurnHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  let result: any = null
  
  try {
    const { slug } = req.params
    const { platform } = req.body
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug, platform, params: req.params, body: req.body }

    if (!slug) {
      result = { 
        status: 'error', 
        message: 'Missing game slug' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'shareForExtraTurn',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    if (!platform) {
      result = { 
        status: 'error', 
        message: 'Missing platform' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'shareForExtraTurn',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    result = await gameG1Service.shareForExtraTurn(
      req, 
      slug, 
      platform
    )
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'shareForExtraTurn',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    const userId = req.authorizedUser?.user?.id
    const requestData = { 
      slug: req.params.slug, 
      platform: req.body.platform, 
      params: req.params, 
      body: req.body 
    }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'shareForExtraTurn - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

// API 1: Lấy SpinHistory theo user, game_id và campaign_id
const getSpinHistoryByGameCampaignHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { gameId, campaignId } = req.query

    if (!gameId || !campaignId) {
      return res.status(200).json({
        status: 200,
        error_code: 1,
        message: 'Missing gameId or campaignId'
      })
    }

    const history = await gameG1Service.getSpinHistoryByUserGameCampaign(
      user.id,
      parseInt(gameId as string),
      parseInt(campaignId as string)
    )

    const result = history.map(item => ({
      id: item.id,
      spin_time: item.spinTime,
      prize_id: item.prizeId,
      prize_name: item.prize?.name || null,
      voucher_code: item.voucherCode,
      voucher_name: item.voucherName,
      voucher_link: item.voucherLink,
      status: item.status,
      claimed_at: item.claimedAt,
      description: item.description,
      address: item.address
    }))

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

// API 2: Lấy SpinHistory theo user và campaign_id với voucherCode/voucherName
const getSpinHistoryWithVoucherHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { campaignId } = req.query

    if (!campaignId) {
      return res.status(200).json({
        status: 200,
        error_code: 1,
        message: 'Missing campaignId'
      })
    }

    const history = await gameG1Service.getSpinHistoryWithVoucherByUserCampaign(
      user.id,
      parseInt(campaignId as string)
    )

    const result = history.map(item => ({
      id: item.id,
      spin_time: item.spinTime,
      prize_id: item.prizeId,
      prize_name: item.prize?.name || null,
      voucher_code: item.voucherCode,
      voucher_name: item.voucherName,
      voucher_link: item.voucherLink,
      status: item.status,
      claimed_at: item.claimedAt,
      game_id: item.gameId,
      campaign_id: item.campaignId
    }))

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

export { 
  initGameHandler, 
  playGameHandler, 
  claimVoucherHandler, 
  shareForExtraTurnHandler,
  getSpinHistoryByGameCampaignHandler,
  getSpinHistoryWithVoucherHandler
} 